/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.druid.indexing.overlord.hrtr;

import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.SettableFuture;
import org.apache.druid.indexer.TaskLocation;
import org.apache.druid.indexer.TaskStatus;
import org.apache.druid.indexing.common.task.Task;
import org.apache.druid.indexing.overlord.TaskRunnerWorkItem;
import org.apache.druid.java.util.common.DateTimes;
import org.joda.time.DateTime;

import javax.annotation.Nullable;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Work item representing a task in the HighThroughputTaskRunner.
 * Thread-safe implementation using atomic references for state management.
 */
public class TaskWorkItem extends TaskRunnerWorkItem
{
  private final String taskId;
  private final Task task;
  private final SettableFuture<TaskStatus> result;
  private final DateTime createdTime;
  private final AtomicReference<DateTime> queueInsertionTime;
  private final AtomicReference<TaskState> taskState;
  private final AtomicReference<String> assignedWorker;
  private final AtomicReference<TaskLocation> location;
  private final AtomicReference<DateTime> assignmentTime;
  private final AtomicReference<DateTime> runningTime;
  private final AtomicReference<DateTime> completionTime;
  
  public enum TaskState
  {
    PENDING,    // Task is waiting in queue or for assignment
    RUNNING,    // Task is running on a worker
    COMPLETED   // Task has finished (success or failure)
  }
  
  public TaskWorkItem(Task task)
  {
    super(task.getId(), SettableFuture.create());
    this.taskId = task.getId();
    this.task = task;
    this.result = SettableFuture.create();
    this.createdTime = DateTimes.nowUtc();
    this.queueInsertionTime = new AtomicReference<>(DateTimes.nowUtc());
    this.taskState = new AtomicReference<>(TaskState.PENDING);
    this.assignedWorker = new AtomicReference<>();
    this.location = new AtomicReference<>(TaskLocation.unknown());
    this.assignmentTime = new AtomicReference<>();
    this.runningTime = new AtomicReference<>();
    this.completionTime = new AtomicReference<>();
  }
  
  public String getTaskId()
  {
    return taskId;
  }
  
  public Task getTask()
  {
    return task;
  }
  
  @Override
  public ListenableFuture<TaskStatus> getResult()
  {
    return result;
  }
  
  @Override
  public DateTime getCreatedTime()
  {
    return createdTime;
  }
  
  @Override
  public DateTime getQueueInsertionTime()
  {
    return queueInsertionTime.get();
  }
  
  public void setQueueInsertionTime(DateTime time)
  {
    queueInsertionTime.set(time);
  }
  
  public TaskState getTaskState()
  {
    return taskState.get();
  }
  
  /**
   * Atomically transition task state from expected to new state.
   * Returns true if transition was successful, false otherwise.
   */
  public boolean transitionState(TaskState expectedState, TaskState newState)
  {
    boolean success = taskState.compareAndSet(expectedState, newState);
    if (success) {
      DateTime now = DateTimes.nowUtc();
      switch (newState) {
        case RUNNING:
          runningTime.set(now);
          break;
        case COMPLETED:
          completionTime.set(now);
          break;
      }
    }
    return success;
  }
  
  /**
   * Force set task state (use with caution)
   */
  public void setTaskState(TaskState state)
  {
    taskState.set(state);
    DateTime now = DateTimes.nowUtc();
    switch (state) {
      case RUNNING:
        runningTime.set(now);
        break;
      case COMPLETED:
        completionTime.set(now);
        break;
    }
  }
  
  @Nullable
  public String getAssignedWorker()
  {
    return assignedWorker.get();
  }
  
  public void setAssignedWorker(@Nullable String worker)
  {
    assignedWorker.set(worker);
    if (worker != null) {
      assignmentTime.set(DateTimes.nowUtc());
    }
  }
  
  @Override
  public TaskLocation getLocation()
  {
    return location.get();
  }
  
  public void setLocation(TaskLocation location)
  {
    this.location.set(location != null ? location : TaskLocation.unknown());
  }
  
  @Nullable
  public DateTime getAssignmentTime()
  {
    return assignmentTime.get();
  }
  
  @Nullable
  public DateTime getRunningTime()
  {
    return runningTime.get();
  }
  
  @Nullable
  public DateTime getCompletionTime()
  {
    return completionTime.get();
  }
  
  /**
   * Set the final result of the task. This completes the future.
   */
  public void setResult(TaskStatus status)
  {
    setTaskState(TaskState.COMPLETED);
    result.set(status);
  }
  
  /**
   * Get task type from the underlying task
   */
  @Override
  public String getTaskType()
  {
    return task.getType();
  }
  
  /**
   * Get data source from the underlying task
   */
  @Override
  public String getDataSource()
  {
    return task.getDataSource();
  }
  
  /**
   * Calculate how long the task has been pending (waiting for assignment)
   */
  public long getPendingDurationMs()
  {
    DateTime start = queueInsertionTime.get();
    DateTime end = runningTime.get();
    if (end == null) {
      end = DateTimes.nowUtc();
    }
    return end.getMillis() - start.getMillis();
  }
  
  /**
   * Calculate how long the task has been running
   */
  public long getRunningDurationMs()
  {
    DateTime start = runningTime.get();
    if (start == null) {
      return 0;
    }
    
    DateTime end = completionTime.get();
    if (end == null) {
      end = DateTimes.nowUtc();
    }
    
    return end.getMillis() - start.getMillis();
  }
  
  /**
   * Calculate total duration from creation to completion (or now)
   */
  public long getTotalDurationMs()
  {
    DateTime end = completionTime.get();
    if (end == null) {
      end = DateTimes.nowUtc();
    }
    return end.getMillis() - createdTime.getMillis();
  }
  
  /**
   * Check if task is in a pending state (PENDING)
   */
  public boolean isPending()
  {
    return taskState.get() == TaskState.PENDING;
  }
  
  /**
   * Check if task is in a running state (RUNNING)
   */
  public boolean isRunning()
  {
    return taskState.get() == TaskState.RUNNING;
  }
  
  /**
   * Check if task is completed (COMPLETED)
   */
  public boolean isCompleted()
  {
    return taskState.get() == TaskState.COMPLETED;
  }
  
  @Override
  public String toString()
  {
    return "TaskWorkItem{" +
           "taskId='" + taskId + '\'' +
           ", taskState=" + taskState.get() +
           ", assignedWorker='" + assignedWorker.get() + '\'' +
           ", createdTime=" + createdTime +
           ", queueInsertionTime=" + queueInsertionTime.get() +
           ", assignmentTime=" + assignmentTime.get() +
           ", runningTime=" + runningTime.get() +
           ", completionTime=" + completionTime.get() +
           '}';
  }
}
