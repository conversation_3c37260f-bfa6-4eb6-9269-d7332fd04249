/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.druid.indexing.overlord.hrtr;

import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.SettableFuture;
import org.apache.druid.indexer.TaskStatus;
import org.apache.druid.indexing.common.task.Task;
import org.apache.druid.indexing.overlord.TaskRunner;
import org.apache.druid.indexing.overlord.TaskRunnerListener;
import org.apache.druid.indexing.overlord.TaskRunnerWorkItem;
import org.apache.druid.java.util.common.lifecycle.LifecycleStart;
import org.apache.druid.java.util.common.lifecycle.LifecycleStop;
import org.apache.druid.java.util.emitter.EmittingLogger;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * High-throughput, multi-threaded task runner designed to replace HttpRemoteTaskRunner.
 * 
 * Key features:
 * - Lock-free task submission for maximum throughput
 * - Efficient worker selection using concurrent data structures
 * - Asynchronous task assignment with timeout handling
 * - Thread-safe worker state management
 * - Background status callback processing
 * 
 * Task States: PENDING -> RUNNING -> COMPLETED
 * Worker States: READY, PENDING_ASSIGN, BLACKLISTED, LAZY
 */
public class HighThroughputTaskRunner implements TaskRunner
{
  private static final EmittingLogger log = new EmittingLogger(HighThroughputTaskRunner.class);
  
  private static final long ASSIGNMENT_TIMEOUT_MS = 30_000; // 30 seconds
  private static final long BLACKLIST_DURATION_MS = 5 * 60 * 1000; // 5 minutes
  
  // Core data structures for tasks
  private final ConcurrentHashMap<String, TaskWorkItem> allTasks = new ConcurrentHashMap<>();
  private final LinkedBlockingQueue<String> pendingTaskQueue = new LinkedBlockingQueue<>();
  private final ConcurrentHashMap<String, TaskWorkItem> runningTasks = new ConcurrentHashMap<>();
  private final ConcurrentHashMap<String, TaskWorkItem> completedTasks = new ConcurrentHashMap<>();
  
  // Core data structures for workers
  private final ConcurrentHashMap<String, WorkerInfo> allWorkers = new ConcurrentHashMap<>();
  private final ConcurrentSkipListSet<String> readyWorkers = new ConcurrentSkipListSet<>();
  private final ConcurrentHashMap<String, WorkerInfo> pendingAssignWorkers = new ConcurrentHashMap<>();
  private final ConcurrentHashMap<String, WorkerInfo> blacklistedWorkers = new ConcurrentHashMap<>();
  private final ConcurrentHashMap<String, WorkerInfo> lazyWorkers = new ConcurrentHashMap<>();
  
  // Assignment tracking
  private final ConcurrentHashMap<String, PendingAssignment> pendingAssignments = new ConcurrentHashMap<>();
  private final ConcurrentHashMap<String, Set<String>> workerToTasks = new ConcurrentHashMap<>();
  
  // Thread pools
  private final ScheduledExecutorService assignmentExecutor;
  private final ScheduledExecutorService statusCallbackExecutor;
  private final ScheduledExecutorService workerManagementExecutor;
  private final ScheduledExecutorService assignmentPollingExecutor;
  
  // Lifecycle management
  private final AtomicBoolean started = new AtomicBoolean(false);
  private final AtomicBoolean stopped = new AtomicBoolean(false);
  
  // Metrics
  private final AtomicLong tasksSubmitted = new AtomicLong(0);
  private final AtomicLong tasksCompleted = new AtomicLong(0);
  private final AtomicLong assignmentFailures = new AtomicLong(0);
  
  public HighThroughputTaskRunner(
      ScheduledExecutorService assignmentExecutor,
      ScheduledExecutorService statusCallbackExecutor,
      ScheduledExecutorService workerManagementExecutor,
      ScheduledExecutorService assignmentPollingExecutor
  )
  {
    this.assignmentExecutor = assignmentExecutor;
    this.statusCallbackExecutor = statusCallbackExecutor;
    this.workerManagementExecutor = workerManagementExecutor;
    this.assignmentPollingExecutor = assignmentPollingExecutor;
  }
  
  @Override
  @LifecycleStart
  public void start()
  {
    if (!started.compareAndSet(false, true)) {
      log.warn("HighThroughputTaskRunner already started");
      return;
    }
    
    log.info("Starting HighThroughputTaskRunner...");
    
    // Start assignment processing
    startAssignmentProcessing();
    
    // Start worker management
    startWorkerManagement();
    
    log.info("HighThroughputTaskRunner started successfully");
  }
  
  @Override
  @LifecycleStop
  public void stop()
  {
    if (!stopped.compareAndSet(false, true)) {
      log.warn("HighThroughputTaskRunner already stopped");
      return;
    }
    
    log.info("Stopping HighThroughputTaskRunner...");
    
    // Shutdown thread pools gracefully
    assignmentExecutor.shutdown();
    statusCallbackExecutor.shutdown();
    workerManagementExecutor.shutdown();
    assignmentPollingExecutor.shutdown();
    
    log.info("HighThroughputTaskRunner stopped successfully");
  }
  
  /**
   * Add a task to the queue. This method is designed for maximum throughput
   * and is completely non-blocking.
   */
  public ListenableFuture<TaskStatus> add(Task task)
  {
    if (stopped.get()) {
      throw new IllegalStateException("TaskRunner is stopped");
    }
    
    String taskId = task.getId();
    TaskWorkItem workItem = new TaskWorkItem(task);
    
    // Check if task already exists
    TaskWorkItem existing = allTasks.putIfAbsent(taskId, workItem);
    if (existing != null) {
      log.info("Task [%s] already exists, returning existing future", taskId);
      return existing.getResult();
    }
    
    // Add to pending queue (non-blocking)
    boolean queued = pendingTaskQueue.offer(taskId);
    if (!queued) {
      log.error("Failed to queue task [%s] - queue may be full", taskId);
      allTasks.remove(taskId);
      workItem.setResult(TaskStatus.failure(taskId, "Failed to queue task"));
      return workItem.getResult();
    }
    
    tasksSubmitted.incrementAndGet();
    log.debug("Task [%s] added to pending queue", taskId);
    
    return workItem.getResult();
  }
  
  /**
   * Mark a worker as lazy (no new task assignments)
   */
  public void markWorkerLazy(String workerIP)
  {
    WorkerInfo worker = allWorkers.get(workerIP);
    if (worker == null) {
      log.warn("Cannot mark unknown worker [%s] as lazy", workerIP);
      return;
    }
    
    // Move worker to lazy state
    moveWorkerToLazy(worker);
    log.info("Worker [%s] marked as lazy", workerIP);
  }
  
  /**
   * Unmark a worker as lazy (make it ready for assignments)
   */
  public void unMarkWorkerLazy(String workerIP)
  {
    WorkerInfo worker = lazyWorkers.remove(workerIP);
    if (worker == null) {
      log.warn("Worker [%s] is not in lazy state", workerIP);
      return;
    }
    
    // Move worker back to ready state if not blacklisted
    if (!blacklistedWorkers.containsKey(workerIP)) {
      moveWorkerToReady(worker);
      log.info("Worker [%s] unmarked as lazy and moved to ready", workerIP);
    } else {
      log.info("Worker [%s] unmarked as lazy but remains blacklisted", workerIP);
    }
  }
  
  /**
   * Check if a worker is safe to mark as lazy (no running tasks)
   */
  public boolean isWorkerOKForMarkingLazy(String workerIP)
  {
    Set<String> tasks = workerToTasks.get(workerIP);
    if (tasks == null || tasks.isEmpty()) {
      return true;
    }
    
    // Check if any tasks are still running on this worker
    for (String taskId : tasks) {
      TaskWorkItem workItem = runningTasks.get(taskId);
      if (workItem != null) {
        return false; // Worker has running tasks
      }
    }
    
    return true;
  }
  
  /**
   * Get all workers that are ready for task assignment
   */
  public Set<String> getAssignableWorkers()
  {
    return Set.copyOf(readyWorkers);
  }
  
  /**
   * Get all currently running tasks
   */
  public Set<String> getRunningTasks()
  {
    return Set.copyOf(runningTasks.keySet());
  }
  
  /**
   * Get all pending tasks waiting in queue
   */
  public Set<String> getPendingTasks()
  {
    return Set.copyOf(pendingTaskQueue);
  }
  
  // Implementation methods will be added in subsequent files...
  
  private void startAssignmentProcessing()
  {
    // Will be implemented in next iteration
  }
  
  private void startWorkerManagement()
  {
    // Will be implemented in next iteration
  }
  
  private void moveWorkerToLazy(WorkerInfo worker)
  {
    // Will be implemented in next iteration
  }
  
  private void moveWorkerToReady(WorkerInfo worker)
  {
    // Will be implemented in next iteration
  }
  
  // Required TaskRunner interface methods
  @Override
  public ListenableFuture<TaskStatus> run(Task task)
  {
    return add(task);
  }
  
  @Override
  public void registerListener(TaskRunnerListener listener, Executor executor)
  {
    // Will be implemented
  }
  
  @Override
  public void unregisterListener(String listenerId)
  {
    // Will be implemented
  }
  
  @Override
  public Collection<? extends TaskRunnerWorkItem> getRunningTasks()
  {
    return runningTasks.values();
  }
  
  @Override
  public Collection<? extends TaskRunnerWorkItem> getPendingTasks()
  {
    return allTasks.values().stream()
                  .filter(item -> item.getTaskState() == TaskState.PENDING)
                  .toList();
  }
  
  @Override
  public Collection<? extends TaskRunnerWorkItem> getKnownTasks()
  {
    return allTasks.values();
  }
  
  @Override
  public Map<String, Long> getTotalTaskSlotCount()
  {
    // Will be implemented based on worker capacity
    return Map.of();
  }
  
  @Override
  public Map<String, Long> getIdleTaskSlotCount()
  {
    // Will be implemented based on available worker slots
    return Map.of();
  }
  
  @Override
  public Map<String, Long> getUsedTaskSlotCount()
  {
    // Will be implemented based on running tasks
    return Map.of();
  }
  
  @Override
  public Map<String, Long> getLazyTaskSlotCount()
  {
    // Will be implemented based on lazy workers
    return Map.of();
  }
  
  @Override
  public Map<String, Long> getBlacklistedTaskSlotCount()
  {
    // Will be implemented based on blacklisted workers
    return Map.of();
  }
}
