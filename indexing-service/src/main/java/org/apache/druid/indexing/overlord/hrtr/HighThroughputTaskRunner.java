/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.druid.indexing.overlord.hrtr;

import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.SettableFuture;
import org.apache.druid.indexer.TaskStatus;
import org.apache.druid.indexing.common.task.Task;
import org.apache.druid.indexing.overlord.TaskRunner;
import org.apache.druid.indexing.overlord.TaskRunnerListener;
import org.apache.druid.indexing.overlord.TaskRunnerWorkItem;
import org.apache.druid.java.util.common.lifecycle.LifecycleStart;
import org.apache.druid.java.util.common.lifecycle.LifecycleStop;
import org.apache.druid.java.util.emitter.EmittingLogger;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * High-throughput, multi-threaded task runner designed to replace HttpRemoteTaskRunner.
 * 
 * Key features:
 * - Lock-free task submission for maximum throughput
 * - Efficient worker selection using concurrent data structures
 * - Asynchronous task assignment with timeout handling
 * - Thread-safe worker state management
 * - Background status callback processing
 * 
 * Task States: PENDING -> RUNNING -> COMPLETED
 * Worker States: READY, PENDING_ASSIGN, BLACKLISTED, LAZY
 */
public class HighThroughputTaskRunner implements TaskRunner
{
  private static final EmittingLogger log = new EmittingLogger(HighThroughputTaskRunner.class);
  
  private static final long ASSIGNMENT_TIMEOUT_MS = 30_000; // 30 seconds
  private static final long BLACKLIST_DURATION_MS = 5 * 60 * 1000; // 5 minutes
  
  // Consolidated data structures
  private final ConcurrentHashMap<String, TaskWorkItem> tasks = new ConcurrentHashMap<>();
  private final ConcurrentHashMap<String, WorkerInfo> workers = new ConcurrentHashMap<>();

  // High-performance queue for pending tasks
  private final LinkedBlockingQueue<String> pendingTaskQueue = new LinkedBlockingQueue<>();

  // Fast lookup for ready workers (sorted for efficient selection)
  private final ConcurrentSkipListSet<String> readyWorkerIPs = new ConcurrentSkipListSet<>();
  
  // Assignment tracking
  private final ConcurrentHashMap<String, PendingAssignment> pendingAssignments = new ConcurrentHashMap<>();
  private final ConcurrentHashMap<String, Set<String>> workerToTasks = new ConcurrentHashMap<>();
  
  // Thread pools
  private final ScheduledExecutorService assignmentExecutor;
  private final ScheduledExecutorService statusCallbackExecutor;
  private final ScheduledExecutorService workerManagementExecutor;
  private final ScheduledExecutorService assignmentPollingExecutor;
  
  // Lifecycle management
  private final AtomicBoolean started = new AtomicBoolean(false);
  private final AtomicBoolean stopped = new AtomicBoolean(false);
  
  // Metrics
  private final AtomicLong tasksSubmitted = new AtomicLong(0);
  private final AtomicLong tasksCompleted = new AtomicLong(0);
  private final AtomicLong assignmentFailures = new AtomicLong(0);
  
  public HighThroughputTaskRunner(
      ScheduledExecutorService assignmentExecutor,
      ScheduledExecutorService statusCallbackExecutor,
      ScheduledExecutorService workerManagementExecutor,
      ScheduledExecutorService assignmentPollingExecutor
  )
  {
    this.assignmentExecutor = assignmentExecutor;
    this.statusCallbackExecutor = statusCallbackExecutor;
    this.workerManagementExecutor = workerManagementExecutor;
    this.assignmentPollingExecutor = assignmentPollingExecutor;
  }
  
  @Override
  @LifecycleStart
  public void start()
  {
    if (!started.compareAndSet(false, true)) {
      log.warn("HighThroughputTaskRunner already started");
      return;
    }
    
    log.info("Starting HighThroughputTaskRunner...");
    
    // Start assignment processing
    startAssignmentProcessing();
    
    // Start worker management
    startWorkerManagement();
    
    log.info("HighThroughputTaskRunner started successfully");
  }
  
  @Override
  @LifecycleStop
  public void stop()
  {
    if (!stopped.compareAndSet(false, true)) {
      log.warn("HighThroughputTaskRunner already stopped");
      return;
    }
    
    log.info("Stopping HighThroughputTaskRunner...");
    
    // Shutdown thread pools gracefully
    assignmentExecutor.shutdown();
    statusCallbackExecutor.shutdown();
    workerManagementExecutor.shutdown();
    assignmentPollingExecutor.shutdown();
    
    log.info("HighThroughputTaskRunner stopped successfully");
  }
  
  /**
   * Add a task to the queue. This method is designed for maximum throughput
   * and is completely non-blocking.
   */
  public ListenableFuture<TaskStatus> add(Task task)
  {
    if (stopped.get()) {
      throw new IllegalStateException("TaskRunner is stopped");
    }

    String taskId = task.getId();
    TaskWorkItem workItem = new TaskWorkItem(task);

    // Check if task already exists
    TaskWorkItem existing = tasks.putIfAbsent(taskId, workItem);
    if (existing != null) {
      log.info("Task [%s] already exists, returning existing future", taskId);
      return existing.getResult();
    }

    // Add to pending queue (non-blocking)
    boolean queued = pendingTaskQueue.offer(taskId);
    if (!queued) {
      log.error("Failed to queue task [%s] - queue may be full", taskId);
      tasks.remove(taskId);
      workItem.setResult(TaskStatus.failure(taskId, "Failed to queue task"));
      return workItem.getResult();
    }

    tasksSubmitted.incrementAndGet();
    log.debug("Task [%s] added to pending queue", taskId);

    return workItem.getResult();
  }
  
  /**
   * Mark a worker as lazy (no new task assignments)
   */
  public void markWorkerLazy(String workerIP)
  {
    WorkerInfo worker = workers.get(workerIP);
    if (worker == null) {
      log.warn("Cannot mark unknown worker [%s] as lazy", workerIP);
      return;
    }

    // Move worker to lazy state and remove from ready set
    if (worker.transitionState(WorkerInfo.WorkerState.READY, WorkerInfo.WorkerState.LAZY)) {
      readyWorkerIPs.remove(workerIP);
      log.info("Worker [%s] marked as lazy", workerIP);
    } else {
      log.warn("Failed to mark worker [%s] as lazy - current state: [%s]", workerIP, worker.getState());
    }
  }

  /**
   * Unmark a worker as lazy (make it ready for assignments)
   */
  public void unMarkWorkerLazy(String workerIP)
  {
    WorkerInfo worker = workers.get(workerIP);
    if (worker == null || !worker.isLazy()) {
      log.warn("Worker [%s] is not in lazy state", workerIP);
      return;
    }

    // Move worker back to ready state if not blacklisted and has capacity
    if (!worker.isBlacklisted() && worker.hasCapacity()) {
      if (worker.transitionState(WorkerInfo.WorkerState.LAZY, WorkerInfo.WorkerState.READY)) {
        readyWorkerIPs.add(workerIP);
        log.info("Worker [%s] unmarked as lazy and moved to ready", workerIP);
      }
    } else {
      worker.setState(WorkerInfo.WorkerState.READY); // Set state but don't add to ready set
      log.info("Worker [%s] unmarked as lazy but not ready (blacklisted: %s, hasCapacity: %s)",
               workerIP, worker.isBlacklisted(), worker.hasCapacity());
    }
  }
  
  /**
   * Check if a worker is safe to mark as lazy (no running tasks)
   */
  public boolean isWorkerOKForMarkingLazy(String workerIP)
  {
    Set<String> workerTasks = workerToTasks.get(workerIP);
    if (workerTasks == null || workerTasks.isEmpty()) {
      return true;
    }

    // Check if any tasks are still running on this worker
    for (String taskId : workerTasks) {
      TaskWorkItem workItem = tasks.get(taskId);
      if (workItem != null && workItem.isRunning() && workerIP.equals(workItem.getAssignedWorker())) {
        return false; // Worker has running tasks
      }
    }

    return true;
  }
  
  /**
   * Get all workers that are ready for task assignment
   */
  public Set<String> getAssignableWorkers()
  {
    return Set.copyOf(readyWorkerIPs);
  }

  /**
   * Get all currently running tasks
   */
  public Set<String> getRunningTasks()
  {
    return tasks.values().stream()
                .filter(TaskWorkItem::isRunning)
                .map(TaskWorkItem::getTaskId)
                .collect(Collectors.toSet());
  }

  /**
   * Get all pending tasks waiting in queue
   */
  public Set<String> getPendingTasks()
  {
    return Set.copyOf(pendingTaskQueue);
  }

  /**
   * Shutdown a running task. This method blocks until the task is shut down.
   */
  public void shutdown(String taskId)
  {
    TaskWorkItem workItem = tasks.get(taskId);
    if (workItem == null) {
      log.warn("Cannot shutdown unknown task [%s]", taskId);
      return;
    }

    if (!workItem.isRunning()) {
      log.info("Task [%s] is not running, current state: [%s]", taskId, workItem.getTaskState());
      return;
    }

    String workerIP = workItem.getAssignedWorker();
    if (workerIP == null) {
      log.warn("Task [%s] has no assigned worker", taskId);
      return;
    }

    log.info("Shutting down task [%s] on worker [%s]", taskId, workerIP);

    // TODO: Implement actual HTTP shutdown call to worker
    // For now, mark task as completed with failure
    workItem.setResult(org.apache.druid.indexer.TaskStatus.failure(taskId, "Task shutdown requested"));

    // Update worker state
    WorkerInfo worker = workers.get(workerIP);
    if (worker != null) {
      worker.decrementRunningTaskCount();

      // Remove task from worker tracking
      Set<String> workerTasks = workerToTasks.get(workerIP);
      if (workerTasks != null) {
        workerTasks.remove(taskId);
      }

      // Move worker back to ready if it has capacity and is not blacklisted/lazy
      if (worker.hasCapacity() && worker.getState() == WorkerInfo.WorkerState.PENDING_ASSIGN) {
        if (worker.transitionState(WorkerInfo.WorkerState.PENDING_ASSIGN, WorkerInfo.WorkerState.READY)) {
          readyWorkerIPs.add(workerIP);
        }
      }
    }

    log.info("Task [%s] shutdown completed", taskId);
  }
  
  // Helper methods for efficient state management

  /**
   * Get all workers in a specific state
   */
  private List<WorkerInfo> getWorkersByState(WorkerInfo.WorkerState state)
  {
    return workers.values().stream()
                  .filter(worker -> worker.getState() == state)
                  .collect(Collectors.toList());
  }

  /**
   * Get all tasks in a specific state
   */
  private List<TaskWorkItem> getTasksByState(TaskWorkItem.TaskState state)
  {
    return tasks.values().stream()
                .filter(task -> task.getTaskState() == state)
                .collect(Collectors.toList());
  }

  /**
   * Add a worker to the system
   */
  public void addWorker(String workerIP, int capacity, String category)
  {
    WorkerInfo worker = new WorkerInfo(workerIP, capacity, category);
    WorkerInfo existing = workers.putIfAbsent(workerIP, worker);

    if (existing == null) {
      // New worker - add to ready set if it has capacity
      if (worker.hasCapacity()) {
        readyWorkerIPs.add(workerIP);
      }
      log.info("Added new worker [%s] with capacity [%d]", workerIP, capacity);
    } else {
      log.info("Worker [%s] already exists", workerIP);
    }
  }

  /**
   * Remove a worker from the system and handle task reassignment
   */
  public void removeWorker(String workerIP)
  {
    WorkerInfo worker = workers.remove(workerIP);
    if (worker != null) {
      readyWorkerIPs.remove(workerIP);
      log.info("Removing worker [%s] and reassigning tasks", workerIP);

      // Handle task reassignment for removed worker
      reassignTasksFromRemovedWorker(workerIP);

      // Clean up worker tracking
      workerToTasks.remove(workerIP);

    } else {
      log.warn("Attempted to remove unknown worker [%s]", workerIP);
    }
  }

  /**
   * Reassign tasks from a removed worker
   */
  private void reassignTasksFromRemovedWorker(String workerIP)
  {
    Set<String> workerTasks = workerToTasks.get(workerIP);
    if (workerTasks == null || workerTasks.isEmpty()) {
      return;
    }

    log.info("Reassigning [%d] tasks from removed worker [%s]", workerTasks.size(), workerIP);

    for (String taskId : workerTasks) {
      TaskWorkItem workItem = tasks.get(taskId);
      if (workItem == null) {
        continue;
      }

      if (workItem.isPending()) {
        // Task was pending assignment, clear worker assignment and put back in queue
        workItem.setAssignedWorker(null);
        pendingTaskQueue.offer(taskId);
        log.info("Reassigned pending task [%s] back to queue", taskId);

      } else if (workItem.isRunning()) {
        // Task was running, mark as failed and put back in queue for retry
        log.warn("Task [%s] was running on removed worker [%s], marking as failed and retrying", taskId, workerIP);

        // Reset task state to pending
        workItem.setTaskState(TaskWorkItem.TaskState.PENDING);
        workItem.setAssignedWorker(null);
        workItem.setLocation(org.apache.druid.indexer.TaskLocation.unknown());

        // Put back in queue for reassignment
        pendingTaskQueue.offer(taskId);
      }

      // Remove pending assignment if exists
      pendingAssignments.remove(taskId);
    }
  }

  /**
   * Handle worker reappearance - check for tasks that were running on this worker
   */
  public void handleWorkerReappearance(String workerIP, int capacity, String category)
  {
    log.info("Worker [%s] reappeared, checking for previously running tasks", workerIP);

    // Add or update worker
    WorkerInfo existingWorker = workers.get(workerIP);
    if (existingWorker == null) {
      addWorker(workerIP, capacity, category);
    } else {
      log.info("Worker [%s] already exists, updating state", workerIP);
      // Reset worker state if it was blacklisted
      if (existingWorker.isBlacklisted()) {
        existingWorker.resetContinuousFailures();
        existingWorker.setBlacklistedUntil(null);
        if (existingWorker.hasCapacity()) {
          existingWorker.setState(WorkerInfo.WorkerState.READY);
          readyWorkerIPs.add(workerIP);
        }
      }
    }

    // Check for tasks that might have been running on this worker
    recoverTasksForWorker(workerIP);
  }

  /**
   * Recover tasks that might have been running on a reappeared worker
   */
  private void recoverTasksForWorker(String workerIP)
  {
    // Look for tasks that were assigned to this worker but may have been lost
    Set<String> workerTasks = workerToTasks.get(workerIP);
    if (workerTasks == null || workerTasks.isEmpty()) {
      return;
    }

    log.info("Checking [%d] tasks for recovery on worker [%s]", workerTasks.size(), workerIP);

    for (String taskId : new java.util.HashSet<>(workerTasks)) {
      TaskWorkItem workItem = tasks.get(taskId);
      if (workItem == null) {
        workerTasks.remove(taskId);
        continue;
      }

      if (workItem.isRunning() && workerIP.equals(workItem.getAssignedWorker())) {
        // Task should be running on this worker - verify status
        log.info("Task [%s] should be running on recovered worker [%s], will verify status", taskId, workerIP);
        // TODO: Make HTTP call to verify task status on worker

      } else if (workItem.isPending()) {
        // Task was pending on this worker, remove assignment and retry
        workItem.setAssignedWorker(null);
        pendingTaskQueue.offer(taskId);
        workerTasks.remove(taskId);
        log.info("Reset pending task [%s] from recovered worker [%s]", taskId, workerIP);
      }
    }
  }

  /**
   * Start the assignment processing threads
   */
  private void startAssignmentProcessing()
  {
    // Start multiple assignment threads for high throughput
    for (int i = 0; i < 4; i++) { // Configurable thread count
      assignmentExecutor.submit(this::processAssignments);
    }
    log.info("Started assignment processing with 4 threads");
  }

  /**
   * Start worker management threads
   */
  private void startWorkerManagement()
  {
    // Start blacklist cleanup thread
    workerManagementExecutor.scheduleAtFixedRate(
        this::cleanupBlacklistedWorkers,
        60, // Initial delay
        60, // Period
        java.util.concurrent.TimeUnit.SECONDS
    );

    // Start assignment timeout monitoring
    assignmentPollingExecutor.scheduleAtFixedRate(
        this::monitorAssignmentTimeouts,
        5,  // Initial delay
        5,  // Period
        java.util.concurrent.TimeUnit.SECONDS
    );

    log.info("Started worker management threads");
  }

  /**
   * Main assignment processing loop - runs in multiple threads
   */
  private void processAssignments()
  {
    while (!stopped.get()) {
      try {
        // Take a task from the pending queue (blocking)
        String taskId = pendingTaskQueue.poll(1, java.util.concurrent.TimeUnit.SECONDS);
        if (taskId == null) {
          continue; // Timeout, check if stopped
        }

        TaskWorkItem workItem = tasks.get(taskId);
        if (workItem == null || !workItem.isPending()) {
          continue; // Task was removed or already processed
        }

        // Find an available worker
        String workerIP = selectAvailableWorker();
        if (workerIP == null) {
          // No workers available, put task back in queue
          pendingTaskQueue.offer(taskId);
          Thread.sleep(100); // Brief pause to avoid busy waiting
          continue;
        }

        // Attempt to assign task to worker
        if (assignTaskToWorker(workItem, workerIP)) {
          log.debug("Successfully assigned task [%s] to worker [%s]", taskId, workerIP);
        } else {
          // Assignment failed, put task back in queue
          pendingTaskQueue.offer(taskId);
          log.warn("Failed to assign task [%s] to worker [%s]", taskId, workerIP);
        }

      } catch (InterruptedException e) {
        log.info("Assignment processing thread interrupted");
        Thread.currentThread().interrupt();
        break;
      } catch (Exception e) {
        log.error(e, "Error in assignment processing");
      }
    }
  }

  /**
   * Select an available worker for task assignment
   */
  private String selectAvailableWorker()
  {
    // Use the concurrent skip list for O(1) selection
    String workerIP = readyWorkerIPs.pollFirst();
    if (workerIP != null) {
      WorkerInfo worker = workers.get(workerIP);
      if (worker != null && worker.isReady()) {
        return workerIP;
      } else {
        // Worker is no longer ready, try next one
        return selectAvailableWorker();
      }
    }
    return null;
  }

  /**
   * Assign a task to a specific worker
   */
  private boolean assignTaskToWorker(TaskWorkItem workItem, String workerIP)
  {
    WorkerInfo worker = workers.get(workerIP);
    if (worker == null || !worker.isReady()) {
      return false;
    }

    String taskId = workItem.getTaskId();

    // Transition worker to PENDING_ASSIGN state
    if (!worker.transitionState(WorkerInfo.WorkerState.READY, WorkerInfo.WorkerState.PENDING_ASSIGN)) {
      // Worker state changed, put it back in ready set if still ready
      if (worker.isReady()) {
        readyWorkerIPs.add(workerIP);
      }
      return false;
    }

    // Set pending task on worker
    worker.setPendingTaskId(taskId);

    // Update task assignment
    workItem.setAssignedWorker(workerIP);

    // Track assignment
    PendingAssignment assignment = new PendingAssignment(taskId, workerIP, ASSIGNMENT_TIMEOUT_MS);
    pendingAssignments.put(taskId, assignment);

    // Add task to worker tracking
    workerToTasks.computeIfAbsent(workerIP, k -> ConcurrentHashMap.newKeySet()).add(taskId);

    // TODO: Make actual HTTP POST to worker
    // For now, simulate successful assignment
    log.info("Assigned task [%s] to worker [%s]", taskId, workerIP);

    // Start polling for task status
    assignmentPollingExecutor.submit(() -> pollTaskAssignment(assignment));

    return true;
  }

  /**
   * Poll a task assignment until it's confirmed or times out
   */
  private void pollTaskAssignment(PendingAssignment assignment)
  {
    String taskId = assignment.getTaskId();
    String workerIP = assignment.getWorkerIP();

    try {
      while (assignment.isPending() && !assignment.isTimedOut()) {
        Thread.sleep(1000); // Poll every second

        TaskWorkItem workItem = tasks.get(taskId);
        if (workItem == null) {
          assignment.cancel();
          break;
        }

        // Check if task status has been updated by callback
        if (workItem.isRunning()) {
          assignment.transitionState(PendingAssignment.AssignmentState.ASSIGNED,
                                   PendingAssignment.AssignmentState.CONFIRMED);
          log.info("Task [%s] confirmed running on worker [%s]", taskId, workerIP);
          break;
        }

        assignment.updateLastStatusCheck();
      }

      // Handle timeout or failure
      if (assignment.isTimedOut()) {
        handleAssignmentTimeout(assignment);
      }

    } catch (InterruptedException e) {
      log.info("Assignment polling interrupted for task [%s]", taskId);
      Thread.currentThread().interrupt();
    } catch (Exception e) {
      log.error(e, "Error polling assignment for task [%s]", taskId);
      handleAssignmentFailure(assignment);
    } finally {
      pendingAssignments.remove(taskId);
    }
  }

  /**
   * Handle assignment timeout
   */
  private void handleAssignmentTimeout(PendingAssignment assignment)
  {
    String taskId = assignment.getTaskId();
    String workerIP = assignment.getWorkerIP();

    log.warn("Assignment timeout for task [%s] on worker [%s]", taskId, workerIP);

    assignment.setState(PendingAssignment.AssignmentState.FAILED);
    assignmentFailures.incrementAndGet();

    // Reset task and worker state
    resetFailedAssignment(taskId, workerIP);
  }

  /**
   * Handle assignment failure
   */
  private void handleAssignmentFailure(PendingAssignment assignment)
  {
    String taskId = assignment.getTaskId();
    String workerIP = assignment.getWorkerIP();

    log.warn("Assignment failure for task [%s] on worker [%s]", taskId, workerIP);

    assignment.setState(PendingAssignment.AssignmentState.FAILED);
    assignmentFailures.incrementAndGet();

    // Reset task and worker state
    resetFailedAssignment(taskId, workerIP);
  }

  /**
   * Reset task and worker state after failed assignment
   */
  private void resetFailedAssignment(String taskId, String workerIP)
  {
    TaskWorkItem workItem = tasks.get(taskId);
    if (workItem != null && workItem.isPending()) {
      // Clear worker assignment
      workItem.setAssignedWorker(null);

      // Put task back in queue
      pendingTaskQueue.offer(taskId);
    }

    WorkerInfo worker = workers.get(workerIP);
    if (worker != null) {
      // Clear pending task
      worker.setPendingTaskId(null);

      // Increment failure count
      worker.incrementContinuousFailures();

      // Check if worker should be blacklisted
      if (worker.getContinuousFailures() >= 3) { // Configurable threshold
        blacklistWorker(worker);
      } else {
        // Move worker back to ready state
        if (worker.transitionState(WorkerInfo.WorkerState.PENDING_ASSIGN, WorkerInfo.WorkerState.READY)) {
          readyWorkerIPs.add(workerIP);
        }
      }
    }

    // Remove from worker task tracking
    Set<String> workerTasks = workerToTasks.get(workerIP);
    if (workerTasks != null) {
      workerTasks.remove(taskId);
    }
  }

  /**
   * Blacklist a worker due to failures
   */
  private void blacklistWorker(WorkerInfo worker)
  {
    String workerIP = worker.getWorkerIP();

    // Set blacklist expiration time
    org.joda.time.DateTime blacklistUntil = org.apache.druid.java.util.common.DateTimes.nowUtc()
                                                .plus(BLACKLIST_DURATION_MS);
    worker.setBlacklistedUntil(blacklistUntil);

    // Transition to blacklisted state
    worker.setState(WorkerInfo.WorkerState.BLACKLISTED);

    // Remove from ready workers
    readyWorkerIPs.remove(workerIP);

    log.warn("Blacklisted worker [%s] until [%s] due to [%d] continuous failures",
             workerIP, blacklistUntil, worker.getContinuousFailures());
  }

  // Required TaskRunner interface methods
  @Override
  public ListenableFuture<TaskStatus> run(Task task)
  {
    return add(task);
  }
  
  // Task runner listeners for notifications
  private final java.util.concurrent.CopyOnWriteArrayList<java.util.concurrent.atomic.AtomicReference<TaskRunnerListener>> listeners =
      new java.util.concurrent.CopyOnWriteArrayList<>();
  private final ConcurrentHashMap<String, java.util.concurrent.atomic.AtomicReference<TaskRunnerListener>> listenerMap =
      new ConcurrentHashMap<>();

  @Override
  public void registerListener(TaskRunnerListener listener, Executor executor)
  {
    String listenerId = java.util.UUID.randomUUID().toString();
    java.util.concurrent.atomic.AtomicReference<TaskRunnerListener> listenerRef =
        new java.util.concurrent.atomic.AtomicReference<>(listener);

    listeners.add(listenerRef);
    listenerMap.put(listenerId, listenerRef);

    log.info("Registered task runner listener [%s]", listenerId);
  }

  @Override
  public void unregisterListener(String listenerId)
  {
    java.util.concurrent.atomic.AtomicReference<TaskRunnerListener> listenerRef = listenerMap.remove(listenerId);
    if (listenerRef != null) {
      listeners.remove(listenerRef);
      log.info("Unregistered task runner listener [%s]", listenerId);
    } else {
      log.warn("Attempted to unregister unknown listener [%s]", listenerId);
    }
  }

  /**
   * Notify all listeners of task location change
   */
  private void notifyTaskLocationChanged(TaskWorkItem workItem)
  {
    for (java.util.concurrent.atomic.AtomicReference<TaskRunnerListener> listenerRef : listeners) {
      TaskRunnerListener listener = listenerRef.get();
      if (listener != null) {
        try {
          listener.locationChanged(workItem, workItem.getLocation(), workItem.getLocation());
        } catch (Exception e) {
          log.error(e, "Error notifying listener of location change for task [%s]", workItem.getTaskId());
        }
      }
    }
  }

  /**
   * Notify all listeners of task status change
   */
  private void notifyTaskStatusChanged(TaskWorkItem workItem, org.apache.druid.indexer.TaskStatus status)
  {
    for (java.util.concurrent.atomic.AtomicReference<TaskRunnerListener> listenerRef : listeners) {
      TaskRunnerListener listener = listenerRef.get();
      if (listener != null) {
        try {
          listener.statusChanged(workItem, status, status);
        } catch (Exception e) {
          log.error(e, "Error notifying listener of status change for task [%s]", workItem.getTaskId());
        }
      }
    }
  }
  
  @Override
  public Collection<? extends TaskRunnerWorkItem> getRunningTasks()
  {
    return tasks.values().stream()
                .filter(TaskWorkItem::isRunning)
                .collect(Collectors.toList());
  }

  @Override
  public Collection<? extends TaskRunnerWorkItem> getPendingTasks()
  {
    return tasks.values().stream()
                .filter(TaskWorkItem::isPending)
                .collect(Collectors.toList());
  }

  @Override
  public Collection<? extends TaskRunnerWorkItem> getKnownTasks()
  {
    return tasks.values();
  }
  
  /**
   * Clean up expired blacklisted workers
   */
  private void cleanupBlacklistedWorkers()
  {
    try {
      for (WorkerInfo worker : getWorkersByState(WorkerInfo.WorkerState.BLACKLISTED)) {
        if (worker.isBlacklistExpired()) {
          String workerIP = worker.getWorkerIP();

          // Reset failure count and move to ready state
          worker.resetContinuousFailures();
          worker.setBlacklistedUntil(null);

          if (worker.hasCapacity()) {
            if (worker.transitionState(WorkerInfo.WorkerState.BLACKLISTED, WorkerInfo.WorkerState.READY)) {
              readyWorkerIPs.add(workerIP);
              log.info("Worker [%s] removed from blacklist and moved to ready", workerIP);
            }
          } else {
            worker.setState(WorkerInfo.WorkerState.READY);
            log.info("Worker [%s] removed from blacklist but not ready (no capacity)", workerIP);
          }
        }
      }
    } catch (Exception e) {
      log.error(e, "Error cleaning up blacklisted workers");
    }
  }

  /**
   * Monitor assignment timeouts
   */
  private void monitorAssignmentTimeouts()
  {
    try {
      for (PendingAssignment assignment : pendingAssignments.values()) {
        if (assignment.isTimedOut()) {
          log.warn("Detected timed out assignment: [%s]", assignment);
          // The polling thread will handle the timeout
        }
      }
    } catch (Exception e) {
      log.error(e, "Error monitoring assignment timeouts");
    }
  }

  @Override
  public Map<String, Long> getTotalTaskSlotCount()
  {
    Map<String, Long> result = new java.util.HashMap<>();
    for (WorkerInfo worker : workers.values()) {
      result.merge(worker.getCategory(), (long) worker.getCapacity(), Long::sum);
    }
    return result;
  }

  @Override
  public Map<String, Long> getIdleTaskSlotCount()
  {
    Map<String, Long> result = new java.util.HashMap<>();
    for (WorkerInfo worker : workers.values()) {
      if (worker.isReady()) {
        result.merge(worker.getCategory(), (long) worker.getAvailableCapacity(), Long::sum);
      }
    }
    return result;
  }

  @Override
  public Map<String, Long> getUsedTaskSlotCount()
  {
    Map<String, Long> result = new java.util.HashMap<>();
    for (WorkerInfo worker : workers.values()) {
      result.merge(worker.getCategory(), (long) worker.getRunningTaskCount(), Long::sum);
    }
    return result;
  }

  @Override
  public Map<String, Long> getLazyTaskSlotCount()
  {
    Map<String, Long> result = new java.util.HashMap<>();
    for (WorkerInfo worker : getWorkersByState(WorkerInfo.WorkerState.LAZY)) {
      result.merge(worker.getCategory(), (long) worker.getCapacity(), Long::sum);
    }
    return result;
  }

  @Override
  public Map<String, Long> getBlacklistedTaskSlotCount()
  {
    Map<String, Long> result = new java.util.HashMap<>();
    for (WorkerInfo worker : getWorkersByState(WorkerInfo.WorkerState.BLACKLISTED)) {
      result.merge(worker.getCategory(), (long) worker.getCapacity(), Long::sum);
    }
    return result;
  }

  /**
   * Handle task status callback from worker (called by background threads)
   * This method is thread-safe and designed for high-throughput status updates
   */
  public void handleTaskStatusCallback(String taskId, org.apache.druid.indexer.TaskStatus status, String workerIP)
  {
    statusCallbackExecutor.submit(() -> processTaskStatusCallback(taskId, status, workerIP));
  }

  /**
   * Process task status callback in background thread
   */
  private void processTaskStatusCallback(String taskId, org.apache.druid.indexer.TaskStatus status, String workerIP)
  {
    try {
      TaskWorkItem workItem = tasks.get(taskId);
      if (workItem == null) {
        log.warn("Received status callback for unknown task [%s]", taskId);
        return;
      }

      WorkerInfo worker = workers.get(workerIP);
      if (worker == null) {
        log.warn("Received status callback from unknown worker [%s] for task [%s]", workerIP, taskId);
        return;
      }

      log.debug("Processing status callback for task [%s] on worker [%s]: [%s]",
                taskId, workerIP, status.getStatusCode());

      switch (status.getStatusCode()) {
        case RUNNING:
          handleTaskRunningCallback(workItem, worker, status);
          break;
        case SUCCESS:
        case FAILED:
          handleTaskCompletedCallback(workItem, worker, status);
          break;
        default:
          log.warn("Unexpected task status [%s] for task [%s]", status.getStatusCode(), taskId);
      }

    } catch (Exception e) {
      log.error(e, "Error processing status callback for task [%s]", taskId);
    }
  }

  /**
   * Handle RUNNING status callback
   */
  private void handleTaskRunningCallback(TaskWorkItem workItem, WorkerInfo worker, org.apache.druid.indexer.TaskStatus status)
  {
    String taskId = workItem.getTaskId();
    String workerIP = worker.getWorkerIP();

    // Transition task from PENDING to RUNNING
    if (workItem.transitionState(TaskWorkItem.TaskState.PENDING, TaskWorkItem.TaskState.RUNNING)) {
      workItem.setLocation(status.getLocation());
      worker.incrementRunningTaskCount();
      worker.resetContinuousFailures(); // Reset on successful start

      log.info("Task [%s] started running on worker [%s]", taskId, workerIP);

      // Notify listeners
      notifyTaskLocationChanged(workItem);
      notifyTaskStatusChanged(workItem, status);

      // Worker can now accept new assignments (stays in PENDING_ASSIGN until this task completes)
      // This allows multiple tasks per worker

    } else {
      log.warn("Failed to transition task [%s] to RUNNING state, current state: [%s]",
               taskId, workItem.getTaskState());
    }
  }

  /**
   * Handle COMPLETED (SUCCESS/FAILED) status callback
   */
  private void handleTaskCompletedCallback(TaskWorkItem workItem, WorkerInfo worker, org.apache.druid.indexer.TaskStatus status)
  {
    String taskId = workItem.getTaskId();
    String workerIP = worker.getWorkerIP();

    // Transition task to COMPLETED and set result
    if (workItem.transitionState(TaskWorkItem.TaskState.RUNNING, TaskWorkItem.TaskState.COMPLETED)) {
      workItem.setResult(status);
      worker.decrementRunningTaskCount();
      worker.setLastTaskCompletion(org.apache.druid.java.util.common.DateTimes.nowUtc());

      tasksCompleted.incrementAndGet();

      // Notify listeners
      notifyTaskStatusChanged(workItem, status);

      if (status.isSuccess()) {
        worker.resetContinuousFailures();
        log.info("Task [%s] completed successfully on worker [%s]", taskId, workerIP);
      } else {
        worker.incrementContinuousFailures();
        log.warn("Task [%s] failed on worker [%s]: [%s]", taskId, workerIP, status.getErrorMsg());

        // Check if worker should be blacklisted
        if (worker.getContinuousFailures() >= 3) { // Configurable threshold
          blacklistWorker(worker);
        }
      }

      // Remove task from worker tracking
      Set<String> workerTasks = workerToTasks.get(workerIP);
      if (workerTasks != null) {
        workerTasks.remove(taskId);
      }

      // Move worker back to ready if it has capacity and is not blacklisted/lazy
      if (worker.hasCapacity() && !worker.isBlacklisted() && !worker.isLazy()) {
        if (worker.transitionState(WorkerInfo.WorkerState.PENDING_ASSIGN, WorkerInfo.WorkerState.READY)) {
          readyWorkerIPs.add(workerIP);
        }
      }

    } else {
      log.warn("Failed to transition task [%s] to COMPLETED state, current state: [%s]",
               taskId, workItem.getTaskState());
    }
  }
}
