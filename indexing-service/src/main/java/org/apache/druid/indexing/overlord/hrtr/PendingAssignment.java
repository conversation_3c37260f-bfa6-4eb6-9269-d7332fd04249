/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.druid.indexing.overlord.hrtr;

import org.apache.druid.java.util.common.DateTimes;
import org.joda.time.DateTime;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Represents a pending task assignment in the HighThroughputTaskRunner.
 * Tracks the assignment process from initial assignment to confirmation or timeout.
 */
public class PendingAssignment
{
  private final String taskId;
  private final String workerIP;
  private final DateTime assignmentTime;
  private final long timeoutMs;
  
  private final AtomicReference<AssignmentState> state;
  private final AtomicReference<DateTime> lastStatusCheck;
  private final AtomicBoolean cancelled;
  
  public enum AssignmentState
  {
    ASSIGNED,    // Task has been POSTed to worker, waiting for confirmation
    CONFIRMED,   // Worker has confirmed task is running
    FAILED,      // Assignment failed or timed out
    CANCELLED    // Assignment was cancelled
  }
  
  public PendingAssignment(String taskId, String workerIP, long timeoutMs)
  {
    this.taskId = taskId;
    this.workerIP = workerIP;
    this.assignmentTime = DateTimes.nowUtc();
    this.timeoutMs = timeoutMs;
    
    this.state = new AtomicReference<>(AssignmentState.ASSIGNED);
    this.lastStatusCheck = new AtomicReference<>(assignmentTime);
    this.cancelled = new AtomicBoolean(false);
  }
  
  public String getTaskId()
  {
    return taskId;
  }
  
  public String getWorkerIP()
  {
    return workerIP;
  }
  
  public DateTime getAssignmentTime()
  {
    return assignmentTime;
  }
  
  public long getTimeoutMs()
  {
    return timeoutMs;
  }
  
  public AssignmentState getState()
  {
    return state.get();
  }
  
  /**
   * Atomically transition assignment state from expected to new state.
   * Returns true if transition was successful, false otherwise.
   */
  public boolean transitionState(AssignmentState expectedState, AssignmentState newState)
  {
    return state.compareAndSet(expectedState, newState);
  }
  
  /**
   * Force set assignment state (use with caution)
   */
  public void setState(AssignmentState newState)
  {
    state.set(newState);
  }
  
  public DateTime getLastStatusCheck()
  {
    return lastStatusCheck.get();
  }
  
  public void updateLastStatusCheck()
  {
    lastStatusCheck.set(DateTimes.nowUtc());
  }
  
  /**
   * Check if assignment has timed out
   */
  public boolean isTimedOut()
  {
    if (cancelled.get() || state.get() != AssignmentState.ASSIGNED) {
      return false;
    }
    
    long elapsedMs = DateTimes.nowUtc().getMillis() - assignmentTime.getMillis();
    return elapsedMs > timeoutMs;
  }
  
  /**
   * Get remaining time before timeout in milliseconds
   */
  public long getRemainingTimeMs()
  {
    if (cancelled.get() || state.get() != AssignmentState.ASSIGNED) {
      return 0;
    }
    
    long elapsedMs = DateTimes.nowUtc().getMillis() - assignmentTime.getMillis();
    return Math.max(0, timeoutMs - elapsedMs);
  }
  
  /**
   * Get elapsed time since assignment in milliseconds
   */
  public long getElapsedTimeMs()
  {
    return DateTimes.nowUtc().getMillis() - assignmentTime.getMillis();
  }
  
  /**
   * Cancel the assignment
   */
  public void cancel()
  {
    cancelled.set(true);
    state.set(AssignmentState.CANCELLED);
  }
  
  /**
   * Check if assignment is cancelled
   */
  public boolean isCancelled()
  {
    return cancelled.get();
  }
  
  /**
   * Check if assignment is still pending (not confirmed, failed, or cancelled)
   */
  public boolean isPending()
  {
    AssignmentState currentState = state.get();
    return currentState == AssignmentState.ASSIGNED && !cancelled.get();
  }
  
  /**
   * Check if assignment is confirmed
   */
  public boolean isConfirmed()
  {
    return state.get() == AssignmentState.CONFIRMED;
  }
  
  /**
   * Check if assignment has failed
   */
  public boolean isFailed()
  {
    return state.get() == AssignmentState.FAILED;
  }
  
  /**
   * Check if assignment is complete (confirmed, failed, or cancelled)
   */
  public boolean isComplete()
  {
    AssignmentState currentState = state.get();
    return currentState != AssignmentState.ASSIGNED || cancelled.get();
  }
  
  @Override
  public String toString()
  {
    return "PendingAssignment{" +
           "taskId='" + taskId + '\'' +
           ", workerIP='" + workerIP + '\'' +
           ", assignmentTime=" + assignmentTime +
           ", timeoutMs=" + timeoutMs +
           ", state=" + state.get() +
           ", cancelled=" + cancelled.get() +
           ", elapsedMs=" + getElapsedTimeMs() +
           ", remainingMs=" + getRemainingTimeMs() +
           '}';
  }
  
  @Override
  public boolean equals(Object o)
  {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    
    PendingAssignment that = (PendingAssignment) o;
    return taskId.equals(that.taskId) && workerIP.equals(that.workerIP);
  }
  
  @Override
  public int hashCode()
  {
    return taskId.hashCode() * 31 + workerIP.hashCode();
  }
}
