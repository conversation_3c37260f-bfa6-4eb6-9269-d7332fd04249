/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.druid.indexing.overlord.hrtr;

import org.apache.druid.java.util.common.DateTimes;
import org.joda.time.DateTime;

import javax.annotation.Nullable;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Information about a worker in the HighThroughputTaskRunner.
 * Thread-safe implementation using atomic references for state management.
 */
public class WorkerInfo
{
  private final String workerIP;
  private final int capacity;
  private final String category;
  private final DateTime discoveredTime;
  
  private final AtomicReference<WorkerState> state;
  private final AtomicReference<DateTime> lastStateChange;
  private final AtomicReference<DateTime> blacklistedUntil;
  private final AtomicInteger continuousFailures;
  private final AtomicInteger runningTaskCount;
  private final AtomicReference<DateTime> lastTaskCompletion;
  private final AtomicReference<String> pendingTaskId;
  
  public enum WorkerState
  {
    READY,          // Available for new task assignments
    PENDING_ASSIGN, // Has a pending task assignment, waiting for confirmation
    BLACKLISTED,    // Temporarily unavailable due to failures
    LAZY            // Marked by external system, no new assignments
  }
  
  public WorkerInfo(String workerIP, int capacity, String category)
  {
    this.workerIP = workerIP;
    this.capacity = capacity;
    this.category = category;
    this.discoveredTime = DateTimes.nowUtc();
    
    this.state = new AtomicReference<>(WorkerState.READY);
    this.lastStateChange = new AtomicReference<>(discoveredTime);
    this.blacklistedUntil = new AtomicReference<>();
    this.continuousFailures = new AtomicInteger(0);
    this.runningTaskCount = new AtomicInteger(0);
    this.lastTaskCompletion = new AtomicReference<>(discoveredTime);
    this.pendingTaskId = new AtomicReference<>();
  }
  
  public String getWorkerIP()
  {
    return workerIP;
  }
  
  public int getCapacity()
  {
    return capacity;
  }
  
  public String getCategory()
  {
    return category;
  }
  
  public DateTime getDiscoveredTime()
  {
    return discoveredTime;
  }
  
  public WorkerState getState()
  {
    return state.get();
  }
  
  /**
   * Atomically transition worker state from expected to new state.
   * Returns true if transition was successful, false otherwise.
   */
  public boolean transitionState(WorkerState expectedState, WorkerState newState)
  {
    boolean success = state.compareAndSet(expectedState, newState);
    if (success) {
      lastStateChange.set(DateTimes.nowUtc());
      
      // Clear pending task when leaving PENDING_ASSIGN state
      if (expectedState == WorkerState.PENDING_ASSIGN && newState != WorkerState.PENDING_ASSIGN) {
        pendingTaskId.set(null);
      }
    }
    return success;
  }
  
  /**
   * Force set worker state (use with caution)
   */
  public void setState(WorkerState newState)
  {
    WorkerState oldState = state.getAndSet(newState);
    lastStateChange.set(DateTimes.nowUtc());
    
    // Clear pending task when leaving PENDING_ASSIGN state
    if (oldState == WorkerState.PENDING_ASSIGN && newState != WorkerState.PENDING_ASSIGN) {
      pendingTaskId.set(null);
    }
  }
  
  public DateTime getLastStateChange()
  {
    return lastStateChange.get();
  }
  
  @Nullable
  public DateTime getBlacklistedUntil()
  {
    return blacklistedUntil.get();
  }
  
  public void setBlacklistedUntil(@Nullable DateTime until)
  {
    blacklistedUntil.set(until);
  }
  
  /**
   * Check if worker is currently blacklisted
   */
  public boolean isBlacklisted()
  {
    DateTime until = blacklistedUntil.get();
    return until != null && DateTimes.nowUtc().isBefore(until);
  }
  
  /**
   * Check if blacklist has expired
   */
  public boolean isBlacklistExpired()
  {
    DateTime until = blacklistedUntil.get();
    return until != null && DateTimes.nowUtc().isAfter(until);
  }
  
  public int getContinuousFailures()
  {
    return continuousFailures.get();
  }
  
  public void incrementContinuousFailures()
  {
    continuousFailures.incrementAndGet();
  }
  
  public void resetContinuousFailures()
  {
    continuousFailures.set(0);
  }
  
  public int getRunningTaskCount()
  {
    return runningTaskCount.get();
  }
  
  public void incrementRunningTaskCount()
  {
    runningTaskCount.incrementAndGet();
  }
  
  public void decrementRunningTaskCount()
  {
    runningTaskCount.updateAndGet(count -> Math.max(0, count - 1));
  }
  
  public void setRunningTaskCount(int count)
  {
    runningTaskCount.set(Math.max(0, count));
  }
  
  /**
   * Check if worker has capacity for more tasks
   */
  public boolean hasCapacity()
  {
    return runningTaskCount.get() < capacity;
  }
  
  /**
   * Get available capacity (remaining task slots)
   */
  public int getAvailableCapacity()
  {
    return Math.max(0, capacity - runningTaskCount.get());
  }
  
  public DateTime getLastTaskCompletion()
  {
    return lastTaskCompletion.get();
  }
  
  public void setLastTaskCompletion(DateTime time)
  {
    lastTaskCompletion.set(time);
  }
  
  @Nullable
  public String getPendingTaskId()
  {
    return pendingTaskId.get();
  }
  
  public void setPendingTaskId(@Nullable String taskId)
  {
    pendingTaskId.set(taskId);
  }
  
  /**
   * Check if worker is ready for new assignments
   */
  public boolean isReady()
  {
    return state.get() == WorkerState.READY && hasCapacity() && !isBlacklisted();
  }
  
  /**
   * Check if worker is in pending assign state
   */
  public boolean isPendingAssign()
  {
    return state.get() == WorkerState.PENDING_ASSIGN;
  }
  
  /**
   * Check if worker is lazy
   */
  public boolean isLazy()
  {
    return state.get() == WorkerState.LAZY;
  }
  
  /**
   * Calculate how long worker has been in current state
   */
  public long getTimeInCurrentStateMs()
  {
    return DateTimes.nowUtc().getMillis() - lastStateChange.get().getMillis();
  }
  
  /**
   * Get worker utilization as a percentage (0.0 to 1.0)
   */
  public double getUtilization()
  {
    if (capacity == 0) {
      return 0.0;
    }
    return (double) runningTaskCount.get() / capacity;
  }
  
  @Override
  public String toString()
  {
    return "WorkerInfo{" +
           "workerIP='" + workerIP + '\'' +
           ", capacity=" + capacity +
           ", category='" + category + '\'' +
           ", state=" + state.get() +
           ", runningTaskCount=" + runningTaskCount.get() +
           ", continuousFailures=" + continuousFailures.get() +
           ", blacklistedUntil=" + blacklistedUntil.get() +
           ", pendingTaskId='" + pendingTaskId.get() + '\'' +
           ", lastStateChange=" + lastStateChange.get() +
           '}';
  }
  
  @Override
  public boolean equals(Object o)
  {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    
    WorkerInfo that = (WorkerInfo) o;
    return workerIP.equals(that.workerIP);
  }
  
  @Override
  public int hashCode()
  {
    return workerIP.hashCode();
  }
}
