/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.druid.indexing.overlord.hrtr;

import org.apache.druid.indexer.TaskLocation;
import org.apache.druid.indexer.TaskStatus;
import org.apache.druid.indexing.common.task.NoopTask;
import org.apache.druid.indexing.common.task.Task;
import org.junit.Assert;
import org.junit.Test;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class TaskWorkItemTest
{
  @Test
  public void testBasicTaskWorkItem()
  {
    Task task = NoopTask.create("test-task", 0);
    TaskWorkItem workItem = new TaskWorkItem(task);
    
    Assert.assertEquals(task.getId(), workItem.getTaskId());
    Assert.assertEquals(task, workItem.getTask());
    Assert.assertEquals(TaskWorkItem.TaskState.PENDING, workItem.getTaskState());
    Assert.assertTrue(workItem.isPending());
    Assert.assertFalse(workItem.isRunning());
    Assert.assertFalse(workItem.isCompleted());
    Assert.assertNull(workItem.getAssignedWorker());
    Assert.assertEquals(TaskLocation.unknown(), workItem.getLocation());
  }

  @Test
  public void testStateTransitions()
  {
    Task task = NoopTask.create("state-test-task", 0);
    TaskWorkItem workItem = new TaskWorkItem(task);
    
    // Test PENDING -> RUNNING transition
    Assert.assertTrue(workItem.transitionState(TaskWorkItem.TaskState.PENDING, TaskWorkItem.TaskState.RUNNING));
    Assert.assertEquals(TaskWorkItem.TaskState.RUNNING, workItem.getTaskState());
    Assert.assertTrue(workItem.isRunning());
    Assert.assertNotNull(workItem.getRunningTime());
    
    // Test invalid transition (should fail)
    Assert.assertFalse(workItem.transitionState(TaskWorkItem.TaskState.PENDING, TaskWorkItem.TaskState.COMPLETED));
    Assert.assertEquals(TaskWorkItem.TaskState.RUNNING, workItem.getTaskState());
    
    // Test RUNNING -> COMPLETED transition
    Assert.assertTrue(workItem.transitionState(TaskWorkItem.TaskState.RUNNING, TaskWorkItem.TaskState.COMPLETED));
    Assert.assertEquals(TaskWorkItem.TaskState.COMPLETED, workItem.getTaskState());
    Assert.assertTrue(workItem.isCompleted());
    Assert.assertNotNull(workItem.getCompletionTime());
  }

  @Test
  public void testWorkerAssignment()
  {
    Task task = NoopTask.create("assignment-test-task", 0);
    TaskWorkItem workItem = new TaskWorkItem(task);
    
    String workerIP = "test-worker-1";
    workItem.setAssignedWorker(workerIP);
    
    Assert.assertEquals(workerIP, workItem.getAssignedWorker());
    Assert.assertNotNull(workItem.getAssignmentTime());
    
    // Clear assignment
    workItem.setAssignedWorker(null);
    Assert.assertNull(workItem.getAssignedWorker());
  }

  @Test
  public void testLocationManagement()
  {
    Task task = NoopTask.create("location-test-task", 0);
    TaskWorkItem workItem = new TaskWorkItem(task);
    
    TaskLocation location = TaskLocation.create("test-host", 8080, 8443);
    workItem.setLocation(location);
    
    Assert.assertEquals(location, workItem.getLocation());
    
    // Test null location handling
    workItem.setLocation(null);
    Assert.assertEquals(TaskLocation.unknown(), workItem.getLocation());
  }

  @Test
  public void testResultSetting()
  {
    Task task = NoopTask.create("result-test-task", 0);
    TaskWorkItem workItem = new TaskWorkItem(task);
    
    TaskStatus successStatus = TaskStatus.success(task.getId());
    workItem.setResult(successStatus);
    
    Assert.assertEquals(TaskWorkItem.TaskState.COMPLETED, workItem.getTaskState());
    Assert.assertTrue(workItem.getResult().isDone());
  }

  @Test
  public void testDurationCalculations() throws InterruptedException
  {
    Task task = NoopTask.create("duration-test-task", 0);
    TaskWorkItem workItem = new TaskWorkItem(task);
    
    // Initial state - only pending duration should be > 0
    Thread.sleep(10);
    Assert.assertTrue(workItem.getPendingDurationMs() > 0);
    Assert.assertEquals(0, workItem.getRunningDurationMs());
    Assert.assertTrue(workItem.getTotalDurationMs() > 0);
    
    // Transition to running
    workItem.transitionState(TaskWorkItem.TaskState.PENDING, TaskWorkItem.TaskState.RUNNING);
    Thread.sleep(10);
    
    Assert.assertTrue(workItem.getRunningDurationMs() > 0);
    Assert.assertTrue(workItem.getTotalDurationMs() > workItem.getPendingDurationMs());
    
    // Transition to completed
    workItem.transitionState(TaskWorkItem.TaskState.RUNNING, TaskWorkItem.TaskState.COMPLETED);
    
    long finalRunningDuration = workItem.getRunningDurationMs();
    long finalTotalDuration = workItem.getTotalDurationMs();
    
    // Durations should be fixed after completion
    Thread.sleep(10);
    Assert.assertEquals(finalRunningDuration, workItem.getRunningDurationMs());
    Assert.assertEquals(finalTotalDuration, workItem.getTotalDurationMs());
  }

  @Test
  public void testConcurrentStateTransitions() throws InterruptedException
  {
    Task task = NoopTask.create("concurrent-test-task", 0);
    TaskWorkItem workItem = new TaskWorkItem(task);
    
    int numThreads = 10;
    CountDownLatch latch = new CountDownLatch(numThreads);
    AtomicInteger successCount = new AtomicInteger(0);
    
    // Multiple threads trying to transition from PENDING to RUNNING
    for (int i = 0; i < numThreads; i++) {
      new Thread(() -> {
        try {
          if (workItem.transitionState(TaskWorkItem.TaskState.PENDING, TaskWorkItem.TaskState.RUNNING)) {
            successCount.incrementAndGet();
          }
        } finally {
          latch.countDown();
        }
      }).start();
    }
    
    Assert.assertTrue("Concurrent transitions timed out", latch.await(5, TimeUnit.SECONDS));
    
    // Only one thread should have succeeded
    Assert.assertEquals(1, successCount.get());
    Assert.assertEquals(TaskWorkItem.TaskState.RUNNING, workItem.getTaskState());
  }

  @Test
  public void testTaskTypeAndDataSource()
  {
    Task task = NoopTask.create("type-test-task", 0);
    TaskWorkItem workItem = new TaskWorkItem(task);
    
    Assert.assertEquals(task.getType(), workItem.getTaskType());
    Assert.assertEquals(task.getDataSource(), workItem.getDataSource());
  }

  @Test
  public void testToString()
  {
    Task task = NoopTask.create("toString-test-task", 0);
    TaskWorkItem workItem = new TaskWorkItem(task);
    
    String toString = workItem.toString();
    Assert.assertTrue(toString.contains("toString-test-task"));
    Assert.assertTrue(toString.contains("PENDING"));
    Assert.assertNotNull(toString);
  }

  @Test
  public void testQueueInsertionTime()
  {
    Task task = NoopTask.create("queue-time-test-task", 0);
    TaskWorkItem workItem = new TaskWorkItem(task);
    
    org.joda.time.DateTime originalTime = workItem.getQueueInsertionTime();
    Assert.assertNotNull(originalTime);
    
    org.joda.time.DateTime newTime = org.apache.druid.java.util.common.DateTimes.nowUtc().plusMinutes(1);
    workItem.setQueueInsertionTime(newTime);
    
    Assert.assertEquals(newTime, workItem.getQueueInsertionTime());
  }

  @Test
  public void testForceStateSet()
  {
    Task task = NoopTask.create("force-state-test-task", 0);
    TaskWorkItem workItem = new TaskWorkItem(task);
    
    // Force set to RUNNING (bypassing normal transition)
    workItem.setTaskState(TaskWorkItem.TaskState.RUNNING);
    Assert.assertEquals(TaskWorkItem.TaskState.RUNNING, workItem.getTaskState());
    Assert.assertNotNull(workItem.getRunningTime());
    
    // Force set to COMPLETED
    workItem.setTaskState(TaskWorkItem.TaskState.COMPLETED);
    Assert.assertEquals(TaskWorkItem.TaskState.COMPLETED, workItem.getTaskState());
    Assert.assertNotNull(workItem.getCompletionTime());
  }
}
