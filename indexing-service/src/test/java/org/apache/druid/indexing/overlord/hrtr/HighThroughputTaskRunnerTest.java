/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.druid.indexing.overlord.hrtr;

import com.google.common.util.concurrent.ListenableFuture;
import org.apache.druid.indexer.TaskLocation;
import org.apache.druid.indexer.TaskState;
import org.apache.druid.indexer.TaskStatus;
import org.apache.druid.indexing.common.task.NoopTask;
import org.apache.druid.indexing.common.task.Task;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class HighThroughputTaskRunnerTest
{
  private HighThroughputTaskRunner taskRunner;
  private ScheduledExecutorService assignmentExecutor;
  private ScheduledExecutorService statusCallbackExecutor;
  private ScheduledExecutorService workerManagementExecutor;
  private ScheduledExecutorService assignmentPollingExecutor;

  @Before
  public void setUp()
  {
    assignmentExecutor = Executors.newScheduledThreadPool(4);
    statusCallbackExecutor = Executors.newScheduledThreadPool(2);
    workerManagementExecutor = Executors.newScheduledThreadPool(2);
    assignmentPollingExecutor = Executors.newScheduledThreadPool(4);

    taskRunner = new HighThroughputTaskRunner(
        assignmentExecutor,
        statusCallbackExecutor,
        workerManagementExecutor,
        assignmentPollingExecutor
    );

    taskRunner.start();
  }

  @After
  public void tearDown()
  {
    if (taskRunner != null) {
      taskRunner.stop();
    }
    
    shutdownExecutor(assignmentExecutor);
    shutdownExecutor(statusCallbackExecutor);
    shutdownExecutor(workerManagementExecutor);
    shutdownExecutor(assignmentPollingExecutor);
  }

  private void shutdownExecutor(ScheduledExecutorService executor)
  {
    if (executor != null) {
      executor.shutdown();
      try {
        if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
          executor.shutdownNow();
        }
      } catch (InterruptedException e) {
        executor.shutdownNow();
        Thread.currentThread().interrupt();
      }
    }
  }

  @Test
  public void testBasicTaskSubmission()
  {
    Task task = NoopTask.create("test-task-1", 0);
    
    ListenableFuture<TaskStatus> future = taskRunner.add(task);
    
    Assert.assertNotNull(future);
    Assert.assertFalse(future.isDone());
    
    // Verify task is in pending state
    Set<String> pendingTasks = taskRunner.getPendingTasks();
    Assert.assertTrue(pendingTasks.contains(task.getId()));
  }

  @Test
  public void testDuplicateTaskSubmission()
  {
    Task task = NoopTask.create("test-task-duplicate", 0);
    
    ListenableFuture<TaskStatus> future1 = taskRunner.add(task);
    ListenableFuture<TaskStatus> future2 = taskRunner.add(task);
    
    // Should return the same future for duplicate task
    Assert.assertSame(future1, future2);
  }

  @Test
  public void testWorkerManagement()
  {
    // Add workers
    taskRunner.addWorker("worker1", 2, "default");
    taskRunner.addWorker("worker2", 3, "default");
    
    Set<String> assignableWorkers = taskRunner.getAssignableWorkers();
    Assert.assertEquals(2, assignableWorkers.size());
    Assert.assertTrue(assignableWorkers.contains("worker1"));
    Assert.assertTrue(assignableWorkers.contains("worker2"));
    
    // Mark worker as lazy
    taskRunner.markWorkerLazy("worker1");
    assignableWorkers = taskRunner.getAssignableWorkers();
    Assert.assertEquals(1, assignableWorkers.size());
    Assert.assertTrue(assignableWorkers.contains("worker2"));
    
    // Unmark worker as lazy
    taskRunner.unMarkWorkerLazy("worker1");
    assignableWorkers = taskRunner.getAssignableWorkers();
    Assert.assertEquals(2, assignableWorkers.size());
  }

  @Test
  public void testWorkerLazyCheck()
  {
    taskRunner.addWorker("worker1", 2, "default");
    
    // Worker should be OK to mark as lazy when no tasks are running
    Assert.assertTrue(taskRunner.isWorkerOKForMarkingLazy("worker1"));
    
    // Add a task and simulate it running on the worker
    Task task = NoopTask.create("test-task-running", 0);
    taskRunner.add(task);
    
    // Simulate task status callback to mark it as running
    TaskStatus runningStatus = TaskStatus.running(task.getId());
    taskRunner.handleTaskStatusCallback(task.getId(), runningStatus, "worker1");
    
    // Worker should not be OK to mark as lazy when tasks are running
    // Note: This test may need adjustment based on actual task assignment logic
  }

  @Test
  public void testHighThroughputTaskSubmission() throws InterruptedException
  {
    int numTasks = 1000;
    CountDownLatch latch = new CountDownLatch(numTasks);
    AtomicInteger successCount = new AtomicInteger(0);
    
    // Submit many tasks concurrently
    for (int i = 0; i < numTasks; i++) {
      final int taskNum = i;
      new Thread(() -> {
        try {
          Task task = NoopTask.create("high-throughput-task-" + taskNum, 0);
          ListenableFuture<TaskStatus> future = taskRunner.add(task);
          Assert.assertNotNull(future);
          successCount.incrementAndGet();
        } catch (Exception e) {
          // Task submission should not fail
          Assert.fail("Task submission failed: " + e.getMessage());
        } finally {
          latch.countDown();
        }
      }).start();
    }
    
    // Wait for all submissions to complete
    Assert.assertTrue("Task submissions timed out", latch.await(30, TimeUnit.SECONDS));
    Assert.assertEquals("Not all tasks were submitted successfully", numTasks, successCount.get());
    
    // Verify all tasks are tracked
    Assert.assertEquals(numTasks, taskRunner.getKnownTasks().size());
  }

  @Test
  public void testTaskStatusCallbacks()
  {
    taskRunner.addWorker("worker1", 2, "default");
    
    Task task = NoopTask.create("callback-test-task", 0);
    taskRunner.add(task);
    
    // Simulate RUNNING status callback
    TaskStatus runningStatus = TaskStatus.running(task.getId());
    taskRunner.handleTaskStatusCallback(task.getId(), runningStatus, "worker1");
    
    // Verify task is in running state
    Set<String> runningTasks = taskRunner.getRunningTasks();
    Assert.assertTrue(runningTasks.contains(task.getId()));
    
    // Simulate SUCCESS status callback
    TaskStatus successStatus = TaskStatus.success(task.getId());
    taskRunner.handleTaskStatusCallback(task.getId(), successStatus, "worker1");
    
    // Verify task is no longer in running state
    runningTasks = taskRunner.getRunningTasks();
    Assert.assertFalse(runningTasks.contains(task.getId()));
  }

  @Test
  public void testWorkerRemovalAndTaskReassignment()
  {
    taskRunner.addWorker("worker1", 2, "default");
    taskRunner.addWorker("worker2", 2, "default");
    
    Task task = NoopTask.create("reassignment-test-task", 0);
    taskRunner.add(task);
    
    // Remove a worker
    taskRunner.removeWorker("worker1");
    
    // Verify worker is no longer assignable
    Set<String> assignableWorkers = taskRunner.getAssignableWorkers();
    Assert.assertFalse(assignableWorkers.contains("worker1"));
    Assert.assertTrue(assignableWorkers.contains("worker2"));
  }

  @Test
  public void testWorkerReappearance()
  {
    // Add worker
    taskRunner.addWorker("worker1", 2, "default");
    
    // Remove worker
    taskRunner.removeWorker("worker1");
    Assert.assertFalse(taskRunner.getAssignableWorkers().contains("worker1"));
    
    // Worker reappears
    taskRunner.handleWorkerReappearance("worker1", 2, "default");
    Assert.assertTrue(taskRunner.getAssignableWorkers().contains("worker1"));
  }

  @Test
  public void testTaskShutdown()
  {
    taskRunner.addWorker("worker1", 2, "default");
    
    Task task = NoopTask.create("shutdown-test-task", 0);
    taskRunner.add(task);
    
    // Simulate task running
    TaskStatus runningStatus = TaskStatus.running(task.getId());
    taskRunner.handleTaskStatusCallback(task.getId(), runningStatus, "worker1");
    
    // Shutdown task
    taskRunner.shutdown(task.getId());
    
    // Verify task is no longer running
    Set<String> runningTasks = taskRunner.getRunningTasks();
    Assert.assertFalse(runningTasks.contains(task.getId()));
  }

  @Test
  public void testConcurrentWorkerOperations() throws InterruptedException
  {
    int numWorkers = 100;
    CountDownLatch latch = new CountDownLatch(numWorkers * 3); // add, lazy, unlazy operations
    
    // Perform concurrent worker operations
    for (int i = 0; i < numWorkers; i++) {
      final String workerIP = "concurrent-worker-" + i;
      
      // Add worker
      new Thread(() -> {
        try {
          taskRunner.addWorker(workerIP, 2, "default");
        } finally {
          latch.countDown();
        }
      }).start();
      
      // Mark lazy
      new Thread(() -> {
        try {
          Thread.sleep(10); // Small delay to ensure worker is added first
          taskRunner.markWorkerLazy(workerIP);
        } catch (InterruptedException e) {
          Thread.currentThread().interrupt();
        } finally {
          latch.countDown();
        }
      }).start();
      
      // Unmark lazy
      new Thread(() -> {
        try {
          Thread.sleep(20); // Small delay to ensure worker is marked lazy first
          taskRunner.unMarkWorkerLazy(workerIP);
        } catch (InterruptedException e) {
          Thread.currentThread().interrupt();
        } finally {
          latch.countDown();
        }
      }).start();
    }
    
    // Wait for all operations to complete
    Assert.assertTrue("Concurrent worker operations timed out", latch.await(30, TimeUnit.SECONDS));
    
    // Verify final state - all workers should be assignable
    Set<String> assignableWorkers = taskRunner.getAssignableWorkers();
    Assert.assertEquals(numWorkers, assignableWorkers.size());
  }
}
